--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

local RUI = LibStub('AceAddon-3.0'):GetAddon('RetailUI')
local moduleName = 'EditorMode'
local Module = RUI:NewModule(moduleName, 'AceConsole-3.0', 'AceHook-3.0', 'AceEvent-3.0')

local UnitFrameModule, CastingBarModule, ActionBarModule, MinimapModule, QuestTrackerModule, BuffFrameModule

Module.editorGridFrame = nil

local function CreateEditorGridFrame()
    local editorGridFrame = CreateFrame("Frame", 'RUI_EditorGridFrame', UIParent)
    editorGridFrame:SetPoint("TOPLEFT", 0, 0)
    editorGridFrame:SetSize(GetScreenWidth(), GetScreenHeight())
    editorGridFrame:SetFrameLevel(0)
    editorGridFrame:SetFrameStrata("BACKGROUND")

    -- Grid constants from settings
    local GRID_SIZE = RUI.DB.profile.gridSize or 32
    local GRID_ALPHA = RUI.DB.profile.gridAlpha or 0.4
    local screenWidth = GetScreenWidth()
    local screenHeight = GetScreenHeight()

    -- Calculate offset to center the grid
    local centerX = screenWidth / 2
    local centerY = screenHeight / 2
    local offsetX = centerX % GRID_SIZE
    local offsetY = centerY % GRID_SIZE

    -- Create programmatic grid lines that match the exact grid size
    editorGridFrame.gridLines = {}

    -- Create vertical grid lines
    local numVerticalLines = math.ceil(screenWidth / GRID_SIZE) + 2
    for i = 0, numVerticalLines do
        local x = centerX - (centerX % GRID_SIZE) + (i - math.floor(numVerticalLines/2)) * GRID_SIZE
        if x >= -GRID_SIZE and x <= screenWidth + GRID_SIZE then
            local line = editorGridFrame:CreateTexture(nil, "BACKGROUND")
            line:SetPoint("TOPLEFT", editorGridFrame, "TOPLEFT", x, 0)
            line:SetSize(1, screenHeight)
            line:SetColorTexture(0.5, 0.5, 0.5, GRID_ALPHA * 0.8)
            table.insert(editorGridFrame.gridLines, line)
        end
    end

    -- Create horizontal grid lines
    local numHorizontalLines = math.ceil(screenHeight / GRID_SIZE) + 2
    for i = 0, numHorizontalLines do
        local y = centerY - (centerY % GRID_SIZE) + (i - math.floor(numHorizontalLines/2)) * GRID_SIZE
        if y >= -GRID_SIZE and y <= screenHeight + GRID_SIZE then
            local line = editorGridFrame:CreateTexture(nil, "BACKGROUND")
            line:SetPoint("TOPLEFT", editorGridFrame, "TOPLEFT", 0, -y)
            line:SetSize(screenWidth, 1)
            line:SetColorTexture(0.5, 0.5, 0.5, GRID_ALPHA * 0.8)
            table.insert(editorGridFrame.gridLines, line)
        end
    end

    -- Create center lines at exact screen center
    -- Vertical center line
    do
        local centerLineV = editorGridFrame:CreateTexture(nil, "BORDER")
        centerLineV:SetPoint("CENTER", editorGridFrame, "CENTER", 0, 0)
        centerLineV:SetSize(2, screenHeight)
        centerLineV:SetColorTexture(1, 1, 0, 0.8) -- Yellow color with higher alpha
        table.insert(editorGridFrame.gridLines, centerLineV)
    end

    -- Horizontal center line
    do
        local centerLineH = editorGridFrame:CreateTexture(nil, "BORDER")
        centerLineH:SetPoint("CENTER", editorGridFrame, "CENTER", 0, 0)
        centerLineH:SetSize(screenWidth, 2)
        centerLineH:SetColorTexture(1, 1, 0, 0.8) -- Yellow color with higher alpha
        table.insert(editorGridFrame.gridLines, centerLineH)
    end

    editorGridFrame:Hide()
    return editorGridFrame
end

-- Function to clear existing grid lines
local function ClearGridLines(editorGridFrame)
    if editorGridFrame and editorGridFrame.gridLines then
        for _, line in ipairs(editorGridFrame.gridLines) do
            if line then
                line:Hide()
                -- Don't set parent to nil for textures, just hide them
                -- The texture will be garbage collected when the reference is removed
            end
        end
        editorGridFrame.gridLines = {}
    end
end

-- Function to recreate grid lines with current settings
local function RefreshGridLines(editorGridFrame)
    if not editorGridFrame then
        return
    end

    -- Clear existing lines first
    ClearGridLines(editorGridFrame)

    -- Grid constants from settings
    local GRID_SIZE = RUI.DB.profile.gridSize or 32
    local GRID_ALPHA = RUI.DB.profile.gridAlpha or 0.4
    local screenWidth = GetScreenWidth()
    local screenHeight = GetScreenHeight()

    -- Calculate offset to center the grid
    local centerX = screenWidth / 2
    local centerY = screenHeight / 2

    -- Initialize grid lines table
    editorGridFrame.gridLines = {}

    -- Create vertical grid lines
    local numVerticalLines = math.ceil(screenWidth / GRID_SIZE) + 2
    for i = 0, numVerticalLines do
        local x = centerX - (centerX % GRID_SIZE) + (i - math.floor(numVerticalLines/2)) * GRID_SIZE
        if x >= -GRID_SIZE and x <= screenWidth + GRID_SIZE then
            local line = editorGridFrame:CreateTexture(nil, "BACKGROUND")
            line:SetPoint("TOPLEFT", editorGridFrame, "TOPLEFT", x, 0)
            line:SetSize(1, screenHeight)
            line:SetColorTexture(0.5, 0.5, 0.5, GRID_ALPHA * 0.8)
            table.insert(editorGridFrame.gridLines, line)
        end
    end

    -- Create horizontal grid lines
    local numHorizontalLines = math.ceil(screenHeight / GRID_SIZE) + 2
    for i = 0, numHorizontalLines do
        local y = centerY - (centerY % GRID_SIZE) + (i - math.floor(numHorizontalLines/2)) * GRID_SIZE
        if y >= -GRID_SIZE and y <= screenHeight + GRID_SIZE then
            local line = editorGridFrame:CreateTexture(nil, "BACKGROUND")
            line:SetPoint("TOPLEFT", editorGridFrame, "TOPLEFT", 0, -y)
            line:SetSize(screenWidth, 1)
            line:SetColorTexture(0.5, 0.5, 0.5, GRID_ALPHA * 0.8)
            table.insert(editorGridFrame.gridLines, line)
        end
    end

    -- Create center lines at exact screen center
    -- Vertical center line
    do
        local centerLineV = editorGridFrame:CreateTexture(nil, "BORDER")
        centerLineV:SetPoint("CENTER", editorGridFrame, "CENTER", 0, 0)
        centerLineV:SetSize(2, screenHeight)
        centerLineV:SetColorTexture(1, 1, 0, 0.8) -- Yellow color with higher alpha
        table.insert(editorGridFrame.gridLines, centerLineV)
    end

    -- Horizontal center line
    do
        local centerLineH = editorGridFrame:CreateTexture(nil, "BORDER")
        centerLineH:SetPoint("CENTER", editorGridFrame, "CENTER", 0, 0)
        centerLineH:SetSize(screenWidth, 2)
        centerLineH:SetColorTexture(1, 1, 0, 0.8) -- Yellow color with higher alpha
        table.insert(editorGridFrame.gridLines, centerLineH)
    end
end

function Module:OnEnable()
    UnitFrameModule      = RUI:GetModule("UnitFrame")
    CastingBarModule     = RUI:GetModule("CastingBar")
    ActionBarModule      = RUI:GetModule("ActionBar")
    MinimapModule        = RUI:GetModule("Minimap")
    QuestTrackerModule   = RUI:GetModule("QuestTracker")
    BuffFrameModule      = RUI:GetModule("BuffFrame")

    -- Try to get SwingTimer module, but don't fail if it's not available
    SwingTimerModule     = RUI:GetModule("SwingTimer", true) -- Silent mode to prevent errors

    self.editorGridFrame = CreateEditorGridFrame()
end

function Module:OnDisable() end

-- Public function to refresh the grid with current settings
function Module:RefreshGrid()
    if self.editorGridFrame and self.editorGridFrame:IsShown() then
        RefreshGridLines(self.editorGridFrame)
    end
end

function Module:Show()
    if InCombatLockdown() then
        self:Printf(DEFAULT_CHAT_FRAME, "Cannot open settings while in combat")
        return
    end

    -- Refresh grid with current settings before showing
    self:RefreshGrid()
    self.editorGridFrame:Show()

    ActionBarModule:ShowEditorTest()
    UnitFrameModule:ShowEditorTest()
    CastingBarModule:ShowEditorTest()
    if SwingTimerModule then SwingTimerModule:ShowEditorTest() end
    MinimapModule:ShowEditorTest()
    QuestTrackerModule:ShowEditorTest()
    BuffFrameModule:ShowEditorTest()

    -- Show center dots for guidance
    RUI:ShowCenterDots()
end

function Module:Hide()
    self.editorGridFrame:Hide()

    -- Hide center dots
    RUI:HideCenterDots()

    ActionBarModule:HideEditorTest(true)
    UnitFrameModule:HideEditorTest(true)
    CastingBarModule:HideEditorTest(true)
    if SwingTimerModule then SwingTimerModule:HideEditorTest(true) end
    MinimapModule:HideEditorTest(true)
    QuestTrackerModule:HideEditorTest(true)
    BuffFrameModule:HideEditorTest(true)
end

function Module:IsShown()
    return self.editorGridFrame and self.editorGridFrame:IsShown()
end
