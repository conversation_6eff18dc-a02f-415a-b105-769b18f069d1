--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

local RUI = LibStub('AceAddon-3.0'):GetAddon('RetailUI')
local moduleName = 'SwingTimer'
local Module = RUI:NewModule(moduleName, 'AceConsole-3.0', 'AceHook-3.0', 'AceEvent-3.0')

Module.swingTimerFrame = nil
Module.timers = {}

-- Timer types
local TIMER_MAINHAND = 1
local TIMER_OFFHAND = 2
local TIMER_RANGED = 3

-- Timer data structure
local timerData = {
    [TIMER_MAINHAND] = { speed = 0, lastSwing = 0, nextSwing = 0, name = "Main Hand" },
    [TIMER_OFFHAND] = { speed = 0, lastSwing = 0, nextSwing = 0, name = "Off Hand" },
    [TIMER_RANGED] = { speed = 0, lastSwing = 0, nextSwing = 0, name = "Ranged" }
}

-- Colors for each timer type
local timerColors = {
    [TIMER_MAINHAND] = { 0.8, 0.2, 0.2, 1 }, -- Red
    [TIMER_OFFHAND] = { 0.2, 0.2, 0.8, 1 },  -- Blue
    [TIMER_RANGED] = { 0.2, 0.8, 0.2, 1 }    -- Green
}

local function CreateSwingTimer(timerType, width, height)
    local timer = CreateFrame("StatusBar", 'RUI_SwingTimer_' .. timerType, UIParent)
    timer:SetSize(width, height)
    timer:SetMinMaxValues(0.0, 1.0)
    timer:SetFrameLevel(timer:GetParent():GetFrameLevel() + 1)
    
    -- Create manual progress bar for WotLK 3.3.5 compatibility
    timer.progressBackground = timer:CreateTexture(nil, "BACKGROUND")
    timer.progressBackground:SetAllPoints(timer)
    timer.progressBackground:SetColorTexture(0.2, 0.2, 0.2, 0.8) -- Dark background
    
    timer.progressTexture = timer:CreateTexture(nil, "ARTWORK")
    timer.progressTexture:SetPoint("LEFT", timer, "LEFT", 0, 0)
    timer.progressTexture:SetHeight(height)
    timer.progressTexture:SetWidth(0) -- Start with 0 width
    timer.progressTexture:SetColorTexture(unpack(timerColors[timerType]))
    
    -- Border texture
    timer.borderTexture = timer:CreateTexture(nil, "BORDER")
    timer.borderTexture:SetAllPoints(timer)
    timer.borderTexture:SetTexture("Interface\\AddOns\\RetailUI\\Textures\\UI\\ActionBarHorizontal.blp")
    timer.borderTexture:SetTexCoord(0, 512 / 512, 14 / 2048, 85 / 2048)
    
    -- Timer text
    timer.timerText = timer:CreateFontString(nil, "OVERLAY", 'GameFontHighlightSmall')
    timer.timerText:SetPoint("LEFT", timer, "LEFT", 4, 0)
    timer.timerText:SetJustifyH("LEFT")
    timer.timerText:SetText(timerData[timerType].name)
    
    -- Time remaining text
    timer.timeText = timer:CreateFontString(nil, "OVERLAY", 'GameFontHighlightSmall')
    timer.timeText:SetPoint("RIGHT", timer, "RIGHT", -4, 0)
    timer.timeText:SetJustifyH("RIGHT")
    timer.timeText:SetText("")
    
    timer.timerType = timerType
    timer:Hide() -- Start hidden
    
    return timer
end

local function GetWeaponName(slot)
    local itemLink = GetInventoryItemLink("player", slot)
    if itemLink then
        local itemName = GetItemInfo(itemLink)
        return itemName or "Unknown"
    end
    return "None"
end

local function UpdateWeaponSpeeds()
    -- Get main hand and off hand speeds
    local mainSpeed, offSpeed = UnitAttackSpeed("player")
    timerData[TIMER_MAINHAND].speed = mainSpeed or 0
    timerData[TIMER_OFFHAND].speed = offSpeed or 0

    -- Get ranged speed - UnitRangedDamage returns multiple values, speed is the 3rd
    local _, _, rangedSpeed = UnitRangedDamage("player")
    timerData[TIMER_RANGED].speed = rangedSpeed or 0

    -- Update weapon names
    timerData[TIMER_MAINHAND].name = GetWeaponName(16) -- Main hand
    timerData[TIMER_RANGED].name = GetWeaponName(18) -- Ranged

    -- Update timer visibility based on equipped weapons
    local hasMainHand = GetInventoryItemLink("player", 16) ~= nil -- Main hand slot
    local hasOffHand = GetInventoryItemLink("player", 17) ~= nil -- Off hand slot
    local hasRanged = GetInventoryItemLink("player", 18) ~= nil -- Ranged slot

    -- Check if off-hand is actually a weapon (not a shield)
    if hasOffHand then
        local offHandLink = GetInventoryItemLink("player", 17)
        if offHandLink then
            local _, _, _, _, _, _, _, _, equipLoc = GetItemInfo(offHandLink)
            -- Hide off-hand timer if it's a shield or off-hand item (not a weapon)
            if equipLoc == "INVTYPE_SHIELD" or equipLoc == "INVTYPE_HOLDABLE" then
                hasOffHand = false
                timerData[TIMER_OFFHAND].name = "Off Hand"
            else
                timerData[TIMER_OFFHAND].name = GetWeaponName(17)
            end
        end
    else
        timerData[TIMER_OFFHAND].name = "Off Hand"
    end

    -- Update timer text with weapon names
    if Module.timers[TIMER_MAINHAND] then
        Module.timers[TIMER_MAINHAND].timerText:SetText(timerData[TIMER_MAINHAND].name)
        if hasMainHand and timerData[TIMER_MAINHAND].speed > 0 then
            Module.timers[TIMER_MAINHAND]:Show()
        else
            Module.timers[TIMER_MAINHAND]:Hide()
        end
    end

    if Module.timers[TIMER_OFFHAND] then
        Module.timers[TIMER_OFFHAND].timerText:SetText(timerData[TIMER_OFFHAND].name)
        if hasOffHand and timerData[TIMER_OFFHAND].speed > 0 then
            Module.timers[TIMER_OFFHAND]:Show()
        else
            Module.timers[TIMER_OFFHAND]:Hide()
        end
    end

    if Module.timers[TIMER_RANGED] then
        Module.timers[TIMER_RANGED].timerText:SetText(timerData[TIMER_RANGED].name)
        if hasRanged and timerData[TIMER_RANGED].speed > 0 then
            Module.timers[TIMER_RANGED]:Show()
        else
            Module.timers[TIMER_RANGED]:Hide()
        end
    end
end

local function UpdateSwingTimer(timer)
    if not timer or not timer:IsShown() then return end
    
    local timerType = timer.timerType
    local data = timerData[timerType]
    local currentTime = GetTime()
    
    if data.speed <= 0 then
        timer:Hide()
        return
    end
    
    -- Calculate progress
    local timeSinceLastSwing = currentTime - data.lastSwing
    local progress = math.min(timeSinceLastSwing / data.speed, 1.0)
    
    -- Update progress bar manually for WotLK 3.3.5 compatibility
    if timer.progressTexture then
        local progressWidth = progress * timer:GetWidth()
        timer.progressTexture:SetWidth(progressWidth)
    end
    
    -- Update time text
    local timeRemaining = math.max(0, data.speed - timeSinceLastSwing)
    if timeRemaining > 0 then
        timer.timeText:SetText(string.format("%.1f", timeRemaining))
    else
        timer.timeText:SetText("Ready")
    end
end

local function OnSwingTimerUpdate(self, elapsed)
    for _, timer in pairs(Module.timers) do
        UpdateSwingTimer(timer)
    end
end

local function HandleCombatLogEvent(timestamp, eventType, sourceGUID, sourceName, sourceFlags, destGUID, destName, destFlags, ...)
    if sourceGUID ~= UnitGUID("player") then return end

    local currentTime = GetTime()

    if eventType == "SWING_DAMAGE" or eventType == "SWING_MISSED" then
        -- For melee swings, we need to determine if it's main hand or off hand
        -- In WotLK, we can use a simple alternating pattern for dual wield
        -- or check if we have an off-hand weapon equipped
        local hasOffHand = GetInventoryItemLink("player", 17) ~= nil
        if hasOffHand then
            local offHandLink = GetInventoryItemLink("player", 17)
            if offHandLink then
                local _, _, _, _, _, _, _, _, equipLoc = GetItemInfo(offHandLink)
                if equipLoc ~= "INVTYPE_SHIELD" and equipLoc ~= "INVTYPE_HOLDABLE" then
                    -- We have a dual wield setup
                    -- Simple alternating logic - could be improved with more complex detection
                    local timeSinceLastMain = currentTime - timerData[TIMER_MAINHAND].lastSwing
                    local timeSinceLastOff = currentTime - timerData[TIMER_OFFHAND].lastSwing

                    -- If main hand was more recent, this is probably off hand
                    if timeSinceLastMain < timeSinceLastOff and timeSinceLastMain < 0.5 then
                        timerData[TIMER_OFFHAND].lastSwing = currentTime
                        timerData[TIMER_OFFHAND].nextSwing = currentTime + timerData[TIMER_OFFHAND].speed
                    else
                        timerData[TIMER_MAINHAND].lastSwing = currentTime
                        timerData[TIMER_MAINHAND].nextSwing = currentTime + timerData[TIMER_MAINHAND].speed
                    end
                else
                    -- Shield or off-hand item, only main hand swings
                    timerData[TIMER_MAINHAND].lastSwing = currentTime
                    timerData[TIMER_MAINHAND].nextSwing = currentTime + timerData[TIMER_MAINHAND].speed
                end
            end
        else
            -- No off-hand, definitely main hand
            timerData[TIMER_MAINHAND].lastSwing = currentTime
            timerData[TIMER_MAINHAND].nextSwing = currentTime + timerData[TIMER_MAINHAND].speed
        end
    elseif eventType == "RANGE_DAMAGE" or eventType == "RANGE_MISSED" then
        -- Ranged attack
        timerData[TIMER_RANGED].lastSwing = currentTime
        timerData[TIMER_RANGED].nextSwing = currentTime + timerData[TIMER_RANGED].speed
    end
end

function Module:OnEnable()
    self:RegisterEvent("PLAYER_ENTERING_WORLD")
    self:RegisterEvent("UNIT_INVENTORY_CHANGED")
    self:RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED")
    self:RegisterEvent("PLAYER_REGEN_ENABLED")
    self:RegisterEvent("PLAYER_REGEN_DISABLED")
    
    -- Create the container frame
    self.swingTimerFrame = CreateUIFrame(180, 50, "SwingTimers")
    
    -- Create individual timer bars
    self.timers[TIMER_MAINHAND] = CreateSwingTimer(TIMER_MAINHAND, 180, 12)
    self.timers[TIMER_OFFHAND] = CreateSwingTimer(TIMER_OFFHAND, 180, 12)
    self.timers[TIMER_RANGED] = CreateSwingTimer(TIMER_RANGED, 180, 12)
    
    -- Position the timers relative to the container
    self.timers[TIMER_MAINHAND]:SetPoint("TOP", self.swingTimerFrame, "TOP", 0, 0)
    self.timers[TIMER_OFFHAND]:SetPoint("TOP", self.timers[TIMER_MAINHAND], "BOTTOM", 0, -2)
    self.timers[TIMER_RANGED]:SetPoint("TOP", self.timers[TIMER_OFFHAND], "BOTTOM", 0, -2)
    
    -- Set up update script
    self.swingTimerFrame:SetScript("OnUpdate", OnSwingTimerUpdate)
end

function Module:OnDisable()
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
    self:UnregisterEvent("UNIT_INVENTORY_CHANGED")
    self:UnregisterEvent("COMBAT_LOG_EVENT_UNFILTERED")
    self:UnregisterEvent("PLAYER_REGEN_ENABLED")
    self:UnregisterEvent("PLAYER_REGEN_DISABLED")
end

function Module:PLAYER_ENTERING_WORLD()
    UpdateWeaponSpeeds()
    
    CheckSettingsExists(Module, { 'swingTimers' })
    
    -- Ensure swing timer positioning is applied after a short delay
    C_Timer.After(0.1, function()
        if self.UpdateWidgets then
            self:UpdateWidgets()
        end
    end)
end

function Module:UNIT_INVENTORY_CHANGED(event, unit)
    if unit == "player" then
        UpdateWeaponSpeeds()
    end
end

function Module:COMBAT_LOG_EVENT_UNFILTERED(event, ...)
    local timestamp, eventType, _, sourceGUID, sourceName, sourceFlags, _, destGUID, destName, destFlags = ...
    HandleCombatLogEvent(timestamp, eventType, sourceGUID, sourceName, sourceFlags, destGUID, destName, destFlags, select(11, ...))
end

function Module:PLAYER_REGEN_DISABLED()
    -- Entering combat - show timers if weapons are equipped
    UpdateWeaponSpeeds()
end

function Module:PLAYER_REGEN_ENABLED()
    -- Leaving combat - could hide timers after a delay if desired
    -- For now, keep them visible
end

function Module:LoadDefaultSettings()
    -- Position the swing timers below the player frame by default
    RUI.DB.profile.widgets.swingTimers = { anchor = "TOPLEFT", posX = 20, posY = -120, scale = 1 }
end

function Module:UpdateWidgets()
    local widgetOptions = RUI.DB.profile.widgets.swingTimers
    self.swingTimerFrame:SetPoint(widgetOptions.anchor, widgetOptions.posX, widgetOptions.posY)
    
    -- Apply scaling (default to 1 if not specified)
    if widgetOptions.scale == nil then
        widgetOptions.scale = 1
    end
    self.swingTimerFrame:SetScale(widgetOptions.scale)
end

function Module:ShowEditorTest()
    HideUIFrame(self.swingTimerFrame)
    -- Show all timers for testing
    for _, timer in pairs(self.timers) do
        timer:Show()
        timer.timerText:SetText(timerData[timer.timerType].name)
        timer.timeText:SetText("1.5")
        if timer.progressTexture then
            timer.progressTexture:SetWidth(timer:GetWidth() * 0.6) -- 60% progress for demo
        end
    end
end

function Module:HideEditorTest(refresh)
    ShowUIFrame(self.swingTimerFrame)
    SaveUIFramePosition(self.swingTimerFrame, 'swingTimers')
    
    -- Reset timer visibility based on actual weapon state
    UpdateWeaponSpeeds()
    
    if refresh then
        self:UpdateWidgets()
    end
end
